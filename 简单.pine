//@version=5
strategy("简单均线策略", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=100)

// 参数：均线周期
short_len = input.int(10, "短期均线周期")
long_len  = input.int(30, "长期均线周期")

// 计算均线
short_ma = ta.sma(close, short_len)
long_ma  = ta.sma(close, long_len)

// 画出均线
plot(short_ma, color=color.blue, linewidth=2, title="短期均线")
plot(long_ma, color=color.orange, linewidth=2, title="长期均线")

// 策略逻辑
if ta.crossover(short_ma, long_ma)
    strategy.entry("做多", strategy.long)

if ta.crossunder(short_ma, long_ma)
    strategy.entry("做空", strategy.short)
f_get_specific_indicator_signal(
     src_series,                // e.g., close (series float)
     indicator_name_const,    // const string (e.g., OPT_MACD_GOLDEN)
     macd_fast_const,         // simple int
     macd_slow_const,         // simple int
     macd_sig_const,          // simple int
     rsi_len_const,           // simple int
     rsi_ob_const,            // simple int
     rsi_os_const,            // simple int
     tf_multiplier_const,     // simple int
     is_long_trade_series     // series bool (true for long, false for short)
     ) =>
    signal_bool = false // This will be a series bool

    if indicator_name_const == OPT_NONE
        signal_bool := true
    else
        // MACD Calculation (uses _tf_multiplier_const which is simple int)
        adj_macd_fast = macd_fast_const * tf_multiplier_const
        adj_macd_slow = macd_slow_const * tf_multiplier_const
        adj_macd_sig = macd_sig_const * tf_multiplier_const
        [macdLine, signalLine, _] = ta.macd(src_series, adj_macd_fast, adj_macd_slow, adj_macd_sig)
        
        is_macd_golden_series = ta.crossover(macdLine, signalLine)
        is_macd_death_series = ta.crossunder(macdLine, signalLine)

        // RSI Calculation (uses _tf_multiplier_const which is simple int)
        adj_rsi_len = rsi_len_const * tf_multiplier_const
        rsi_val_series = ta.rsi(src_series, adj_rsi_len)
        
        // RSI signals are series bool
        is_rsi_oversold_exit_series = ta.crossover(rsi_val_series, rsi_os_const) 
        is_rsi_overbought_exit_series = ta.crossunder(rsi_val_series, rsi_ob_const)
        // For entry conditions into zones (used if strategy logic implies entry upon entering OS/OB)
        // is_rsi_entering_oversold_series = rsi_val_series[1] > rsi_os_const and rsi_val_series <= rsi_os_const
        // is_rsi_entering_overbought_series = rsi_val_series[1] < rsi_ob_const and rsi_val_series >= rsi_ob_const

        // Determine final signal based on indicator_name_const
        if indicator_name_const == OPT_MACD_CROSS
            signal_bool := is_macd_golden_series or is_macd_death_series
        else if indicator_name_const == OPT_MACD_GOLDEN
            signal_bool := is_macd_golden_series
        else if indicator_name_const == OPT_MACD_DEATH
            signal_bool := is_macd_death_series
        else if indicator_name_const == OPT_RSI_OBOS
            // As per video: "开单用死叉" (initial short), "补仓用金叉" (long additions)
            // This means for a LONG trade (initial or addition), we'd look for bullish signals.
            // For a SHORT trade (initial or addition), we'd look for bearish signals.
            if is_long_trade_series // True if we are in a long cycle or opening a long
                signal_bool := is_macd_golden_series or is_rsi_oversold_exit_series // Example: Bullish MACD or RSI exiting oversold
            else // True if we are in a short cycle or opening a short
                signal_bool := is_macd_death_series or is_rsi_overbought_exit_series // Example: Bearish MACD or RSI exiting overbought
    
    signal_bool // Return the series bool