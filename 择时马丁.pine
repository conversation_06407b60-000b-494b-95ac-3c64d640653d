//@version=5
strategy("布欧择时马丁 (BuOu's Timed <PERSON>e)",
         overlay=true,
         pyramiding=10,
         default_qty_type=strategy.cash,
         initial_capital=1000,
         commission_value=0.05,
         commission_type=strategy.commission.percent)

// ========== Inputs: General Settings ==========
grp_general = "General Settings"
i_initial_order_size_usd = input.float(20, "Initial Order Size (USD)", group=grp_general, minval=1)
i_take_profit_pct = input.float(0.9, "Take Profit (%)", group=grp_general, minval=0.01, step=0.01) / 100
i_use_stop_loss = input.bool(false, "Enable Stop Loss", group=grp_general)
i_stop_loss_pct = input.float(20, "Stop Loss (%) from Avg Entry", group=grp_general, minval=0.1, step=0.1) / 100
i_trade_direction = input.string("Long", "Trade Direction", options=["Long", "Short"], group=grp_general)

// ========== Inputs: Initial Entry Indicator ==========
grp_initial_entry = "Initial Entry Indicator"
var GRP_IND_TYPE = "Indicator Type"
var OPT_NONE = "None (Entry on Start)"
var OPT_MACD_CROSS = "MACD Cross"
var OPT_MACD_GOLDEN = "MACD Golden Cross"
var OPT_MACD_DEATH = "MACD Death Cross"
var OPT_RSI_OBOS = "RSI Overbought/Oversold"

i_initial_entry_indicator = input.string(OPT_MACD_DEATH, "Indicator", options=[OPT_NONE, OPT_MACD_CROSS, OPT_MACD_GOLDEN, OPT_MACD_DEATH, OPT_RSI_OBOS], group=grp_initial_entry, inline="init_entry")
i_initial_entry_tf_multiplier = input.int(1, "TF Multiplier", group=grp_initial_entry, minval=1, inline="init_entry") // This is simple int

// ========== Inputs: Averaging Indicators (Orders 2-4: Early) ==========
grp_avg_early = "Averaging Indicator (Orders 2-4: Early)"
i_avg_early_indicator = input.string(OPT_MACD_GOLDEN, "Indicator", options=[OPT_NONE, OPT_MACD_CROSS, OPT_MACD_GOLDEN, OPT_MACD_DEATH, OPT_RSI_OBOS], group=grp_avg_early, inline="avg_early")
i_avg_early_tf_multiplier = input.int(1, "TF Multiplier", group=grp_avg_early, minval=1, inline="avg_early") // This is simple int

// ========== Inputs: Averaging Indicators (Orders 5-10: Late) ==========
grp_avg_late = "Averaging Indicator (Orders 5-10: Late)"
i_avg_late_indicator = input.string(OPT_MACD_GOLDEN, "Indicator", options=[OPT_NONE, OPT_MACD_CROSS, OPT_MACD_GOLDEN, OPT_MACD_DEATH, OPT_RSI_OBOS], group=grp_avg_late, inline="avg_late")
i_avg_late_tf_multiplier = input.int(4, "TF Multiplier", group=grp_avg_late, minval=1, inline="avg_late") // This is simple int

// ========== Inputs: Indicator Parameters ==========
grp_indicators = "Indicator Parameters"
// MACD
i_macd_fast = input.int(12, "MACD Fast Length", group=grp_indicators, minval=1)
i_macd_slow = input.int(26, "MACD Slow Length", group=grp_indicators, minval=1)
i_macd_signal = input.int(9, "MACD Signal Length", group=grp_indicators, minval=1)
// RSI
i_rsi_length = input.int(14, "RSI Length", group=grp_indicators, minval=1)
i_rsi_oversold = input.int(30, "RSI Oversold", group=grp_indicators, minval=1)
i_rsi_overbought = input.int(70, "RSI Overbought", group=grp_indicators, minval=1)

// ========== Inputs: Martingale Order Layers (9 additional orders) ==========
// ... (Martingale layer inputs remain the same, omitted for brevity) ...
// Order 2 (1st Add)
grp_o2 = "Order 2 (1st Add-on)"
i_o2_active = input.bool(true, "Enable", group=grp_o2, inline="o2")
i_o2_drop_pct = input.float(1.2, "Drop Pct from Last Entry", group=grp_o2, minval=0.01, step=0.01, inline="o2") / 100
i_o2_size_mult = input.float(2.0, "Size Multiplier (of Initial)", group=grp_o2, minval=0.1, step=0.1, inline="o2")
// Order 3 (2nd Add)
grp_o3 = "Order 3 (2nd Add-on)"
i_o3_active = input.bool(true, "Enable", group=grp_o3, inline="o3")
i_o3_drop_pct = input.float(1.5, "Drop Pct from Last Entry", group=grp_o3, minval=0.01, step=0.01, inline="o3") / 100
i_o3_size_mult = input.float(3.0, "Size Multiplier (of Initial)", group=grp_o3, minval=0.1, step=0.1, inline="o3")
// Order 4 (3rd Add)
grp_o4 = "Order 4 (3rd Add-on)"
i_o4_active = input.bool(true, "Enable", group=grp_o4, inline="o4")
i_o4_drop_pct = input.float(2.0, "Drop Pct from Last Entry", group=grp_o4, minval=0.01, step=0.01, inline="o4") / 100
i_o4_size_mult = input.float(4.0, "Size Multiplier (of Initial)", group=grp_o4, minval=0.1, step=0.1, inline="o4")
// Order 5 (4th Add)
grp_o5 = "Order 5 (4th Add-on)"
i_o5_active = input.bool(true, "Enable", group=grp_o5, inline="o5")
i_o5_drop_pct = input.float(2.5, "Drop Pct from Last Entry", group=grp_o5, minval=0.01, step=0.01, inline="o5") / 100
i_o5_size_mult = input.float(5.0, "Size Multiplier (of Initial)", group=grp_o5, minval=0.1, step=0.1, inline="o5")
// Order 6 (5th Add)
grp_o6 = "Order 6 (5th Add-on)"
i_o6_active = input.bool(true, "Enable", group=grp_o6, inline="o6")
i_o6_drop_pct = input.float(3.0, "Drop Pct from Last Entry", group=grp_o6, minval=0.01, step=0.01, inline="o6") / 100
i_o6_size_mult = input.float(6.0, "Size Multiplier (of Initial)", group=grp_o6, minval=0.1, step=0.1, inline="o6")
// Order 7 (6th Add)
grp_o7 = "Order 7 (6th Add-on)"
i_o7_active = input.bool(true, "Enable", group=grp_o7, inline="o7")
i_o7_drop_pct = input.float(3.5, "Drop Pct from Last Entry", group=grp_o7, minval=0.01, step=0.01, inline="o7") / 100
i_o7_size_mult = input.float(7.0, "Size Multiplier (of Initial)", group=grp_o7, minval=0.1, step=0.1, inline="o7")
// Order 8 (7th Add)
grp_o8 = "Order 8 (7th Add-on)"
i_o8_active = input.bool(true, "Enable", group=grp_o8, inline="o8")
i_o8_drop_pct = input.float(4.0, "Drop Pct from Last Entry", group=grp_o8, minval=0.01, step=0.01, inline="o8") / 100
i_o8_size_mult = input.float(8.0, "Size Multiplier (of Initial)", group=grp_o8, minval=0.1, step=0.1, inline="o8")
// Order 9 (8th Add)
grp_o9 = "Order 9 (8th Add-on)"
i_o9_active = input.bool(true, "Enable", group=grp_o9, inline="o9")
i_o9_drop_pct = input.float(4.5, "Drop Pct from Last Entry", group=grp_o9, minval=0.01, step=0.01, inline="o9") / 100
i_o9_size_mult = input.float(9.0, "Size Multiplier (of Initial)", group=grp_o9, minval=0.1, step=0.1, inline="o9")
// Order 10 (9th Add)
grp_o10 = "Order 10 (9th Add-on)"
i_o10_active = input.bool(true, "Enable", group=grp_o10, inline="o10")
i_o10_drop_pct = input.float(5.0, "Drop Pct from Last Entry", group=grp_o10, minval=0.01, step=0.01, inline="o10") / 100
i_o10_size_mult = input.float(10.0, "Size Multiplier (of Initial)", group=grp_o10, minval=0.1, step=0.1, inline="o10")

var bool[] arr_order_active = array.from(i_o2_active, i_o3_active, i_o4_active, i_o5_active, i_o6_active, i_o7_active, i_o8_active, i_o9_active, i_o10_active)
var float[] arr_order_drop_pct = array.from(i_o2_drop_pct, i_o3_drop_pct, i_o4_drop_pct, i_o5_drop_pct, i_o6_drop_pct, i_o7_drop_pct, i_o8_drop_pct, i_o9_drop_pct, i_o10_drop_pct)
var float[] arr_order_size_mult = array.from(i_o2_size_mult, i_o3_size_mult, i_o4_size_mult, i_o5_size_mult, i_o6_size_mult, i_o7_size_mult, i_o8_size_mult, i_o9_size_mult, i_o10_size_mult)

// ========== State Variables ==========
var float last_entry_price = na
var float avg_entry_price = na
var float total_qty_contracts = 0
var int   num_open_orders = 0 // Counts number of orders in current cycle (1 to 10)
var float take_profit_price = na
var float stop_loss_price = na
var string current_trade_id = "" // To group entries under one trade for TP/SL

// ========== Indicator Calculations (Refactored Function) ==========
// This function calculates signals for a specific, constant indicator type and a SIMPLE int timeframe multiplier.
// All 'const' parameters here are expected to be 'simple' types (known at compile time or from inputs).
// '_is_long_trade_series' can be 'series bool' as it's used for logical decisions after indicator calculations.
f_get_specific_indicator_signal(
     src_series,
     indicator_name,             // 由 const 改为普通变量
     macd_fast_const,
     macd_slow_const,
     macd_sig_const,
     rsi_len_const,
     rsi_ob_const,
     rsi_os_const,
     tf_multiplier_const,
     is_long_trade_series
     ) =>
     
    signal_bool = false

    // MACD calculation
    adj_macd_fast = macd_fast_const * tf_multiplier_const
    adj_macd_slow = macd_slow_const * tf_multiplier_const
    adj_macd_sig = macd_sig_const * tf_multiplier_const
    [macdLine, signalLine, _] = ta.macd(src_series, adj_macd_fast, adj_macd_slow, adj_macd_sig)

    is_macd_golden_series = ta.crossover(macdLine, signalLine)
    is_macd_death_series = ta.crossunder(macdLine, signalLine)

    // RSI calculation
    adj_rsi_len = rsi_len_const * tf_multiplier_const
    rsi_val_series = ta.rsi(src_series, adj_rsi_len)
    is_rsi_oversold_exit_series = ta.crossover(rsi_val_series, rsi_os_const)
    is_rsi_overbought_exit_series = ta.crossunder(rsi_val_series, rsi_ob_const)

    // Signal logic
    if indicator_name == OPT_NONE
        signal_bool := true
    else if indicator_name == OPT_MACD_CROSS
        signal_bool := is_macd_golden_series or is_macd_death_series
    else if indicator_name == OPT_MACD_GOLDEN
        signal_bool := is_macd_golden_series
    else if indicator_name == OPT_MACD_DEATH
        signal_bool := is_macd_death_series
    else if indicator_name == OPT_RSI_OBOS
        signal_bool := is_long_trade_series ? (is_macd_golden_series or is_rsi_oversold_exit_series) : (is_macd_death_series or is_rsi_overbought_exit_series)

    signal_bool
// ========== Trading Logic ==========
is_long = i_trade_direction == "Long" // This is a series bool

// --- Initial Entry ---
// Calculate initial entry signal using its own tf_multiplier (which is simple int)
initial_entry_signal = f_get_specific_indicator_signal(
     close,
     i_initial_entry_indicator, // const string
     i_macd_fast, i_macd_slow, i_macd_signal, // simple int
     i_rsi_length, i_rsi_overbought, i_rsi_oversold, // simple int
     i_initial_entry_tf_multiplier, // simple int
     is_long // series bool
 )

if num_open_orders == 0 and initial_entry_signal
    entry_size_contracts = i_initial_order_size_usd / close
    current_trade_id := is_long ? "LMart" : "SMart"
    if is_long
        strategy.entry(current_trade_id, strategy.long, qty=entry_size_contracts, comment="Initial Long")
    else
        strategy.entry(current_trade_id, strategy.short, qty=entry_size_contracts, comment="Initial Short")

    last_entry_price := close
    avg_entry_price := close
    total_qty_contracts := entry_size_contracts
    num_open_orders := 1

    if is_long
        take_profit_price := avg_entry_price * (1 + i_take_profit_pct)
        if i_use_stop_loss
            stop_loss_price := avg_entry_price * (1 - i_stop_loss_pct)
    else
        take_profit_price := avg_entry_price * (1 - i_take_profit_pct)
        if i_use_stop_loss
            stop_loss_price := avg_entry_price * (1 + i_stop_loss_pct)

// --- Averaging Down Entries ---
// --- Averaging Down Entries ---
// Pre-calculate signals for BOTH early and late TF multipliers. These are now series bool.
signal_for_avg_early_tf = f_get_specific_indicator_signal (
     close, 
     i_avg_early_indicator, 
     i_macd_fast, 
     i_macd_slow, 
     i_macd_signal, 
     i_rsi_length, 
     i_rsi_overbought, 
     i_rsi_oversold, 
     i_avg_early_tf_multiplier,
     is_long
 )

signal_for_avg_late_tf = f_get_specific_indicator_signal( // Opening parenthesis on the same line
     close,
     i_avg_late_indicator, // const string
     i_macd_fast, i_macd_slow, i_macd_signal, // simple int
     i_rsi_length, i_rsi_overbought, i_rsi_oversold, // simple int
     i_avg_late_tf_multiplier, // simple int (from input, for late orders)
     is_long // series bool
 ) // Closing parenthesis



if num_open_orders > 0 and num_open_orders < 10 // Max 9 additional orders
    current_add_order_idx = num_open_orders - 1 

    if array.get(arr_order_active, current_add_order_idx)
        // 1. Price Condition (series bool)
        price_target_for_add_on = 0.0
        if is_long
            price_target_for_add_on := last_entry_price * (1 - array.get(arr_order_drop_pct, current_add_order_idx))
        else
            price_target_for_add_on := last_entry_price * (1 + array.get(arr_order_drop_pct, current_add_order_idx))

        price_condition_met = (is_long and close <= price_target_for_add_on) or
                              (not is_long and close >= price_target_for_add_on)

        // 2. Indicator Condition (Select pre-calculated signal based on num_open_orders)
        // num_open_orders is series int, so this selection makes 'indicator_condition_met' a series bool
        indicator_condition_met = false
        if num_open_orders < 4 // For Orders 2,3,4 (when num_open_orders is 1,2,3)
            indicator_condition_met := signal_for_avg_early_tf
        else // For Orders 5-10
            indicator_condition_met := signal_for_avg_late_tf
        
        // If the chosen indicator type for early/late is OPT_NONE, it would have already been set to true.

        if price_condition_met and indicator_condition_met
            add_on_size_usd = i_initial_order_size_usd * array.get(arr_order_size_mult, current_add_order_idx)
            add_on_contracts = add_on_size_usd / close

            entry_comment = (is_long ? "Add Long " : "Add Short ") + str.tostring(num_open_orders + 1)
            if is_long
                strategy.entry(current_trade_id, strategy.long, qty=add_on_contracts, comment=entry_comment)
            else
                strategy.entry(current_trade_id, strategy.short, qty=add_on_contracts, comment=entry_comment)

            new_total_qty = total_qty_contracts + add_on_contracts // Approximate for same bar
            avg_entry_price := (total_qty_contracts * avg_entry_price + add_on_contracts * close) / new_total_qty // Approximate
            total_qty_contracts := new_total_qty // Approximate
            last_entry_price := close
            num_open_orders += 1 // This is series int, changes bar to bar

            // Recalculate TP/SL (will use strategy.position_avg_price on next bar if needed)
            if is_long
                take_profit_price := avg_entry_price * (1 + i_take_profit_pct)
                if i_use_stop_loss
                    stop_loss_price := avg_entry_price * (1 - i_stop_loss_pct)
            else
                take_profit_price := avg_entry_price * (1 - i_take_profit_pct)
                if i_use_stop_loss
                    stop_loss_price := avg_entry_price * (1 + i_stop_loss_pct)

// --- Exit Logic ---
if num_open_orders > 0
    // Update with actual broker values if available (after an entry has been processed by TV)
    // This runs at the start of each bar, using values from the previous bar's execution.
    if strategy.position_size != 0 and not na(strategy.position_avg_price) and num_open_orders > 0
        if math.abs(strategy.position_size) != total_qty_contracts or strategy.position_avg_price != avg_entry_price
            // If TV's state differs from our tracked state (e.g., after an entry filled), update our state.
            avg_entry_price := strategy.position_avg_price
            total_qty_contracts := math.abs(strategy.position_size)
            // num_open_orders should align if pyramiding works as expected, but could be resynced if needed.

            if is_long
                take_profit_price := avg_entry_price * (1 + i_take_profit_pct)
                if i_use_stop_loss
                    stop_loss_price := avg_entry_price * (1 - i_stop_loss_pct)
            else
                take_profit_price := avg_entry_price * (1 - i_take_profit_pct)
                if i_use_stop_loss
                    stop_loss_price := avg_entry_price * (1 + i_stop_loss_pct)

    // Take Profit
    tp_hit = (is_long and close >= take_profit_price and not na(take_profit_price)) or 
             (not is_long and close <= take_profit_price and not na(take_profit_price))
    if tp_hit
        strategy.close(current_trade_id, comment="TP Hit")
        // Reset state
        last_entry_price := na
        avg_entry_price := na
        total_qty_contracts := 0
        num_open_orders := 0
        take_profit_price := na
        stop_loss_price := na
        current_trade_id := ""

    // Stop Loss
    if i_use_stop_loss and not na(stop_loss_price)
        sl_hit = (is_long and close <= stop_loss_price) or 
                 (not is_long and close >= stop_loss_price)
        if sl_hit
            strategy.close(current_trade_id, comment="SL Hit")
            // Reset state
            last_entry_price := na
            avg_entry_price := na
            total_qty_contracts := 0
            num_open_orders := 0
            take_profit_price := na
            stop_loss_price := na
            current_trade_id := ""

// If position closed by TV (e.g. end of backtest, manual close from chart)
// and our state still thinks orders are open, reset our state.
if strategy.position_size == 0 and num_open_orders > 0
    last_entry_price := na
    avg_entry_price := na
    total_qty_contracts := 0
    num_open_orders := 0
    take_profit_price := na
    stop_loss_price := na
    current_trade_id := ""

// ========== Plotting ==========
plot(num_open_orders > 0 ? avg_entry_price : na, "Avg Entry Price", color=color.blue, style=plot.style_linebr, linewidth=2)
plot(num_open_orders > 0 ? take_profit_price : na, "Take Profit", color=color.green, style=plot.style_linebr, linewidth=2)
plot(num_open_orders > 0 and i_use_stop_loss ? stop_loss_price : na, "Stop Loss", color=color.red, style=plot.style_linebr, linewidth=2)

float next_potential_entry_price_for_plot = na
string next_potential_entry_label_text = ""

if num_open_orders > 0 and num_open_orders < 10
    current_add_order_idx_plot = num_open_orders - 1 
    if array.get(arr_order_active, current_add_order_idx_plot)
        next_potential_entry_price_for_plot := is_long ?
                                             last_entry_price * (1 - array.get(arr_order_drop_pct, current_add_order_idx_plot)) :
                                             last_entry_price * (1 + array.get(arr_order_drop_pct, current_add_order_idx_plot))
        next_potential_entry_label_text := "NPE " + str.tostring(num_open_orders + 1) 
plot(series=next_potential_entry_price_for_plot,
     title="Next Potential Entry Level", 
     color=color.new(color.orange, 50),
     style=plot.style_circles,
     linewidth=1)

if not na(next_potential_entry_price_for_plot)
    label.new(x=bar_index, y=next_potential_entry_price_for_plot,
              text=next_potential_entry_label_text,
              xloc=xloc.bar_index, yloc=yloc.price,
              color=color.new(color.orange, 100), 
              textcolor=color.new(color.black, 0), 
              style=label.style_none, 
              textalign=text.align_center,
              size=size.small)

plotchar(num_open_orders, "Num Open Orders", "", location.top, size=size.tiny)