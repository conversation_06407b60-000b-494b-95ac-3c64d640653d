// This source code is subject to the terms of the GNU GENERAL PUBLIC LICENSE 3.0 at https://www.gnu.org/licenses/gpl-3.0.html
// @ blockplus
// 代碼來源：https://www.tvcbot.com/knowledgebase/72/PINE教學案例12年200倍的雙均綫TradingView交易策略與自動交易教學or量化交易教學.html

//@version=5
strategy("TVCBOT Strategy Example (SMA Crossing)", overlay=true, initial_capital=5000,pyramiding = 0, currency="USD", default_qty_type=strategy.percent_of_equity, default_qty_value=100,  commission_type=strategy.commission.percent,commission_value=0.1)

// 兩根SMA均綫
trend_type1_length=input.int(25, "MA1长度")
trend_type2_length=input.int(100, "MA2长度")
ma1 = ta.wma(close,trend_type1_length)
ma2 = ta.wma(close,trend_type2_length)

// 把SMA畫出來
p3 = plot(ma1, color= color.new(#00ff00, 0), title="SMA1", linewidth = 1)
p4 = plot(ma2, color= color.new(#ff0000, 0), title="SMA2", linewidth = 1)
fill(p3, p4, color = ma1 > ma2 ? color.new(#00ff00, 50):color.new(#ff0000, 50))

// 參數設定
long_sl_input = input.float(5, title='止损(%)', step=0.1)/100
long_sl_input_level = strategy.position_avg_price * (1 - long_sl_input)
multiplier = input.float(3.5, "SL Mutiplier", minval=1, step=0.1)
ATR_period=input.int(8,"ATR长度", minval=1, step=1)

// 入場條件: ma1上穿ma2 且 close在atr下軌前高之上；
// 出場條件：close在atr下軌前高之下 或 close<固定止損價格
ATRTrail = 0.0
ATRTrail := close - multiplier * ta.atr(ATR_period)
ATRTrailHigh = ta.highest(ATRTrail, 50)
atrd = plot(ATRTrail, color= color.new(#ffffff, 0), title="ATR", linewidth = 1)
fill(p3, atrd, color = color.new(#ffffff, 75))
entry_long=ta.crossover(ma1,ma2) and ATRTrailHigh < close
exit_long = close < ATRTrailHigh or close < long_sl_input_level

// 入場與出場
if entry_long
    strategy.entry("long", strategy.long)
if exit_long
    strategy.close("long", comment="close" )