这个交易策略名为“布欧择时马丁 (<PERSON><PERSON><PERSON><PERSON>'s Timed Martingale)”，它结合了**马丁格尔加仓策略**和**技术指标择时**的思想。

核心逻辑如下：

1.  **基本方向：**
    *   用户可以选择`Long`（做多）或`Short`（做空）作为整个策略的唯一交易方向。一旦设定，策略只会在这个方向上操作。

2.  **初始入场 (第一笔订单)：**
    *   **条件：** 当前没有持仓 (`num_open_orders == 0`)。
    *   **择时：** 根据用户选择的`初始入场指标` (`i_initial_entry_indicator`) 和对应的`时间周期乘数` (`i_initial_entry_tf_multiplier`)。
        *   可选指标包括：无 (立即入场)、MACD交叉、MACD金叉、MACD死叉、RSI超买超卖结合MACD。
        *   `时间周期乘数`允许在当前图表周期的基础上，模拟一个更高时间周期的指标信号 (例如，图表是1小时，乘数是4，则指标基于近似4小时的数据计算)。
    *   **下单：** 如果择时信号触发，则按照`初始订单大小 (USD)` (`i_initial_order_size_usd`) 下第一笔订单。
    *   **状态更新：** 记录最后入场价、平均成本价、持仓数量，并将已开订单数设为1。同时计算初始的止盈价和止损价。

3.  **马丁格尔加仓 (后续订单，最多9笔)：**
    *   **条件：**
        *   当前已有持仓 (`num_open_orders > 0`) 且未达到最大加仓层数 (10层)。
        *   当前加仓层级是启用的 (`i_oX_active`为true)。
    *   **双重触发机制：**
        1.  **价格条件：**
            *   做多时：当前价格比**上一次入场价格**下跌了用户为该层级设定的`下跌百分比` (`i_oX_drop_pct`)。
            *   做空时：当前价格比**上一次入场价格**上涨了用户为该层级设定的`下跌百分比`。
        2.  **指标择时条件：**
            *   **订单 2-4 (早期加仓)：** 使用`早期加仓指标` (`i_avg_early_indicator`) 和其对应的`时间周期乘数` (`i_avg_early_tf_multiplier`) 进行择时。
            *   **订单 5-10 (晚期加仓)：** 使用`晚期加仓指标` (`i_avg_late_indicator`) 和其对应的`时间周期乘数` (`i_avg_late_tf_multiplier`) 进行择时。
            *   这意味着不同阶段的加仓可以使用不同的指标和不同的时间周期来进行更精细的控制。
    *   **下单：** 如果价格条件和指标择时条件**同时满足**，则按照`初始订单大小`乘以该层级设定的`大小乘数` (`i_oX_size_mult`) 进行加仓。
    *   **状态更新：** 更新最后入场价、重新计算平均成本价、增加持仓数量和已开订单数。根据新的平均成本价重新计算止盈价和止损价。

4.  **指标择时函数 (`f_get_specific_indicator_signal`)：**
    *   这个函数是所有择时逻辑的核心。它接收价格序列、指标类型、MACD/RSI参数、时间周期乘数和交易方向。
    *   **MACD信号：** 计算金叉、死叉。
    *   **RSI信号：** 计算RSI值，并判断是否从超卖区上穿或从超买区下穿。
    *   **组合逻辑：**
        *   `None`: 信号恒为真。
        *   `MACD Cross`: 金叉或死叉。
        *   `MACD Golden Cross`: 仅金叉。
        *   `MACD Death Cross`: 仅死叉。
        *   `RSI Overbought/Oversold`:
            *   做多时：MACD金叉 **或** RSI从超卖区上穿。
            *   做空时：MACD死叉 **或** RSI从超买区下穿。

5.  **出场逻辑：**
    *   **止盈 (Take Profit)：**
        *   根据当前持仓的`平均成本价`和用户设定的`止盈百分比` (`i_take_profit_pct`) 计算止盈目标价。
        *   做多时，价格达到或超过止盈价则平仓。
        *   做空时，价格达到或低于止盈价则平仓。
    *   **止损 (Stop Loss)：**
        *   如果启用止损 (`i_use_stop_loss`)。
        *   根据当前持仓的`平均成本价`和用户设定的`止损百分比` (`i_stop_loss_pct`) 计算止损目标价。
        *   做多时，价格达到或低于止损价则平仓。
        *   做空时，价格达到或超过止损价则平仓。
    *   **平仓后重置：** 一旦止盈或止损，所有状态变量（如平均成本、持仓数量、已开订单数等）都会重置，等待下一次初始入场机会。

6.  **状态同步：**
    *   策略会尝试将脚本内部追踪的平均价格和数量与`strategy.position_avg_price`和`strategy.position_size`（即交易所/回测引擎报告的实际持仓状态）同步，以确保计算的准确性。
    *   如果外部原因导致平仓（如手动平仓、回测结束），脚本也会重置内部状态。

**总结来说，这是一个试图通过技术指标来优化马丁格尔策略入场时机的系统。** 它不是盲目地在价格下跌/上涨固定百分比后就加仓，而是要求在价格达到预设的加仓点位**并且**相应的择时指标也发出信号时才进行加仓。通过不同阶段（初始、早期加仓、晚期加仓）使用不同的指标和时间周期配置，策略试图在不同市场深度下采用不同的应对方式。

**风险提示：** 马丁格尔策略本身具有高风险特性。虽然加入了择时，但在极端行情或持续单边行情下，如果连续加仓后仍未达到止盈，可能会导致巨额亏损，甚至爆仓。因此，使用此类策略需要非常谨慎的资金管理和风险控制。