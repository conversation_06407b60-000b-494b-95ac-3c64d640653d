//@version=5
strategy("布欧择时马丁 (<PERSON><PERSON><PERSON><PERSON>'s Timed <PERSON>e)",
         overlay=true,                     // 在主图表上显示交易
         pyramiding=10,                    // 允许最多10层加仓（1个初始订单 + 9个加仓订单）
         default_qty_type=strategy.cash,   // 订单数量类型为现金
         initial_capital=1000,             // 初始资金
         commission_value=0.04,            // 手续费百分比
         commission_type=strategy.commission.percent) // 手续费类型为百分比

// ========== Inputs: General Settings (通用设置) ==========
grp_general = "General Settings"
i_initial_order_size_usd = input.float(20, "Initial Order Size (USD)", group=grp_general, minval=1) // 初始订单大小 (美元)
i_take_profit_pct = input.float(2, "Take Profit (%)", group=grp_general, minval=0.01, step=0.01) / 100 // 止盈百分比 (基于平均入场价)
i_use_stop_loss = input.bool(false, "Enable Stop Loss", group=grp_general) // 是否启用止损
i_stop_loss_pct = input.float(20, "Stop Loss (%) from Avg Entry", group=grp_general, minval=0.1, step=0.1) / 100 // 止损百分比 (基于平均入场价)
i_trade_direction = input.string("Long", "Trade Direction", options=["Long", "Short"], group=grp_general) // 交易方向: "Long" (做多) 或 "Short" (做空)

// ========== Inputs: Initial Entry Indicator (初始入场指标) ==========
grp_initial_entry = "Initial Entry Indicator"
var GRP_IND_TYPE = "Indicator Type" // 指标类型组名 (未直接使用，但作为常量定义)
var OPT_NONE = "None (Entry on Start)" // 选项: 无指标 (开始时即入场)
var OPT_MACD_CROSS = "MACD Cross"      // 选项: MACD 快慢线交叉 (金叉或死叉)
var OPT_MACD_GOLDEN = "MACD Golden Cross"// 选项: MACD 金叉
var OPT_MACD_DEATH = "MACD Death Cross"  // 选项: MACD 死叉
var OPT_RSI_OBOS = "RSI Overbought/Oversold" // 选项: RSI 超买/超卖 (结合MACD交叉)

i_initial_entry_indicator = input.string(OPT_MACD_DEATH, "Indicator", options=[OPT_NONE, OPT_MACD_CROSS, OPT_MACD_GOLDEN, OPT_MACD_DEATH, OPT_RSI_OBOS], group=grp_initial_entry, inline="init_entry") // 初始入场选择的指标
i_initial_entry_tf_multiplier = input.int(1, "TF Multiplier", group=grp_initial_entry, minval=1, inline="init_entry") // 初始入场指标的时间周期乘数 (相对于图表周期)

// ========== Inputs: Averaging Indicators (Orders 2-4: Early) (加仓指标 - 订单2-4: 早期) ==========
grp_avg_early = "Averaging Indicator (Orders 2-4: Early)"
i_avg_early_indicator = input.string(OPT_MACD_GOLDEN, "Indicator", options=[OPT_NONE, OPT_MACD_CROSS, OPT_MACD_GOLDEN, OPT_MACD_DEATH, OPT_RSI_OBOS], group=grp_avg_early, inline="avg_early") // 早期加仓 (订单2-4) 选择的指标
i_avg_early_tf_multiplier = input.int(1, "TF Multiplier", group=grp_avg_early, minval=1, inline="avg_early") // 早期加仓指标的时间周期乘数

// ========== Inputs: Averaging Indicators (Orders 5-10: Late) (加仓指标 - 订单5-10: 晚期) ==========
grp_avg_late = "Averaging Indicator (Orders 5-10: Late)"
i_avg_late_indicator = input.string(OPT_MACD_GOLDEN, "Indicator", options=[OPT_NONE, OPT_MACD_CROSS, OPT_MACD_GOLDEN, OPT_MACD_DEATH, OPT_RSI_OBOS], group=grp_avg_late, inline="avg_late") // 晚期加仓 (订单5-10) 选择的指标
i_avg_late_tf_multiplier = input.int(4, "TF Multiplier", group=grp_avg_late, minval=1, inline="avg_late") // 晚期加仓指标的时间周期乘数

// ========== Inputs: Indicator Parameters (指标参数) ==========
grp_indicators = "Indicator Parameters"
// MACD
i_macd_fast = input.int(12, "MACD Fast Length", group=grp_indicators, minval=1)       // MACD 快线长度
i_macd_slow = input.int(26, "MACD Slow Length", group=grp_indicators, minval=1)       // MACD 慢线长度
i_macd_signal = input.int(9, "MACD Signal Length", group=grp_indicators, minval=1)    // MACD 信号线长度
// RSI
i_rsi_length = input.int(14, "RSI Length", group=grp_indicators, minval=1)            // RSI 长度
i_rsi_oversold = input.int(30, "RSI Oversold", group=grp_indicators, minval=1)        // RSI 超卖阈值
i_rsi_overbought = input.int(70, "RSI Overbought", group=grp_indicators, minval=1)    // RSI 超买阈值

// ========== Inputs: Martingale Order Layers (9 additional orders) (马丁格尔加仓层级 - 9个额外订单) ==========
// Order 2 (1st Add) (订单2 - 第一次加仓)
grp_o2 = "Order 2 (1st Add-on)"
i_o2_active = input.bool(true, "Enable", group=grp_o2, inline="o2")                         // 是否启用订单2
i_o2_drop_pct = input.float(1.2, "Drop Pct from Last Entry", group=grp_o2, minval=0.01, step=0.01, inline="o2") / 100 // 从上次入场价下跌百分比 (触发条件)
i_o2_size_mult = input.float(2.0, "Size Multiplier (of Initial)", group=grp_o2, minval=0.1, step=0.1, inline="o2") // 订单大小乘数 (相对于初始订单)
// Order 3 (2nd Add) (订单3 - 第二次加仓)
grp_o3 = "Order 3 (2nd Add-on)"
i_o3_active = input.bool(true, "Enable", group=grp_o3, inline="o3")
i_o3_drop_pct = input.float(1.5, "Drop Pct from Last Entry", group=grp_o3, minval=0.01, step=0.01, inline="o3") / 100
i_o3_size_mult = input.float(3.0, "Size Multiplier (of Initial)", group=grp_o3, minval=0.1, step=0.1, inline="o3")
// Order 4 (3rd Add) (订单4 - 第三次加仓)
grp_o4 = "Order 4 (3rd Add-on)"
i_o4_active = input.bool(true, "Enable", group=grp_o4, inline="o4")
i_o4_drop_pct = input.float(2.0, "Drop Pct from Last Entry", group=grp_o4, minval=0.01, step=0.01, inline="o4") / 100
i_o4_size_mult = input.float(4.0, "Size Multiplier (of Initial)", group=grp_o4, minval=0.1, step=0.1, inline="o4")
// Order 5 (4th Add) (订单5 - 第四次加仓)
grp_o5 = "Order 5 (4th Add-on)"
i_o5_active = input.bool(true, "Enable", group=grp_o5, inline="o5")
i_o5_drop_pct = input.float(2.5, "Drop Pct from Last Entry", group=grp_o5, minval=0.01, step=0.01, inline="o5") / 100
i_o5_size_mult = input.float(5.0, "Size Multiplier (of Initial)", group=grp_o5, minval=0.1, step=0.1, inline="o5")
// Order 6 (5th Add) (订单6 - 第五次加仓)
grp_o6 = "Order 6 (5th Add-on)"
i_o6_active = input.bool(true, "Enable", group=grp_o6, inline="o6")
i_o6_drop_pct = input.float(3.0, "Drop Pct from Last Entry", group=grp_o6, minval=0.01, step=0.01, inline="o6") / 100
i_o6_size_mult = input.float(6.0, "Size Multiplier (of Initial)", group=grp_o6, minval=0.1, step=0.1, inline="o6")
// Order 7 (6th Add) (订单7 - 第六次加仓)
grp_o7 = "Order 7 (6th Add-on)"
i_o7_active = input.bool(true, "Enable", group=grp_o7, inline="o7")
i_o7_drop_pct = input.float(3.5, "Drop Pct from Last Entry", group=grp_o7, minval=0.01, step=0.01, inline="o7") / 100
i_o7_size_mult = input.float(7.0, "Size Multiplier (of Initial)", group=grp_o7, minval=0.1, step=0.1, inline="o7")
// Order 8 (7th Add) (订单8 - 第七次加仓)
grp_o8 = "Order 8 (7th Add-on)"
i_o8_active = input.bool(true, "Enable", group=grp_o8, inline="o8")
i_o8_drop_pct = input.float(4.0, "Drop Pct from Last Entry", group=grp_o8, minval=0.01, step=0.01, inline="o8") / 100
i_o8_size_mult = input.float(8.0, "Size Multiplier (of Initial)", group=grp_o8, minval=0.1, step=0.1, inline="o8")
// Order 9 (8th Add) (订单9 - 第八次加仓)
grp_o9 = "Order 9 (8th Add-on)"
i_o9_active = input.bool(true, "Enable", group=grp_o9, inline="o9")
i_o9_drop_pct = input.float(4.5, "Drop Pct from Last Entry", group=grp_o9, minval=0.01, step=0.01, inline="o9") / 100
i_o9_size_mult = input.float(9.0, "Size Multiplier (of Initial)", group=grp_o9, minval=0.1, step=0.1, inline="o9")
// Order 10 (9th Add) (订单10 - 第九次加仓)
grp_o10 = "Order 10 (9th Add-on)"
i_o10_active = input.bool(true, "Enable", group=grp_o10, inline="o10")
i_o10_drop_pct = input.float(5.0, "Drop Pct from Last Entry", group=grp_o10, minval=0.01, step=0.01, inline="o10") / 100
i_o10_size_mult = input.float(10.0, "Size Multiplier (of Initial)", group=grp_o10, minval=0.1, step=0.1, inline="o10")

// 将加仓订单的配置存入数组，方便后续循环处理
var bool[] arr_order_active = array.from(i_o2_active, i_o3_active, i_o4_active, i_o5_active, i_o6_active, i_o7_active, i_o8_active, i_o9_active, i_o10_active) // 存储每个加仓订单是否启用的数组
var float[] arr_order_drop_pct = array.from(i_o2_drop_pct, i_o3_drop_pct, i_o4_drop_pct, i_o5_drop_pct, i_o6_drop_pct, i_o7_drop_pct, i_o8_drop_pct, i_o9_drop_pct, i_o10_drop_pct) // 存储每个加仓订单触发的下跌百分比数组
var float[] arr_order_size_mult = array.from(i_o2_size_mult, i_o3_size_mult, i_o4_size_mult, i_o5_size_mult, i_o6_size_mult, i_o7_size_mult, i_o8_size_mult, i_o9_size_mult, i_o10_size_mult) // 存储每个加仓订单大小乘数的数组

// ========== State Variables (状态变量) ==========
// 'var' 关键字确保这些变量在K线之间保持其值
var float last_entry_price = na      // 上一次入场的价格
var float avg_entry_price = na       // 当前持仓的平均入场价格
var float total_qty_contracts = 0  // 当前持仓的总合约数量
var int   num_open_orders = 0      // 当前交易周期中已开订单的数量 (1到10)
var float take_profit_price = na   // 止盈目标价格
var float stop_loss_price = na     // 止损目标价格
var string current_trade_id = ""   // 当前交易周期的唯一ID，用于分组管理订单的止盈/止损

// ========== Indicator Calculations (Refactored Function) (指标计算 - 重构函数) ==========
// 此函数根据指定的指标类型和时间周期乘数计算交易信号。
// 所有 '_const' 后缀的参数都应该是 'simple' 类型 (编译时已知或来自输入)。
// '_is_long_trade_series' 可以是 'series bool'，因为它在指标计算后用于逻辑决策。
f_get_specific_indicator_signal(
     src_series,                // 输入的价格序列 (例如 close)
     indicator_name,            // 指标名称 (来自输入，例如 OPT_MACD_GOLDEN)
     macd_fast_const,           // MACD 快线长度 (simple int)
     macd_slow_const,           // MACD 慢线长度 (simple int)
     macd_sig_const,            // MACD 信号线长度 (simple int)
     rsi_len_const,             // RSI 长度 (simple int)
     rsi_ob_const,              // RSI 超买阈值 (simple int)
     rsi_os_const,              // RSI 超卖阈值 (simple int)
     tf_multiplier_const,       // 时间周期乘数 (simple int)
     is_long_trade_series       // 是否为多头交易 (series bool)
     ) =>
     
    signal_bool = false // 初始化信号为 false

    // MACD 计算
    // 根据时间周期乘数调整MACD参数
    adj_macd_fast = macd_fast_const * tf_multiplier_const
    adj_macd_slow = macd_slow_const * tf_multiplier_const
    adj_macd_sig = macd_sig_const * tf_multiplier_const
    [macdLine, signalLine, _] = ta.macd(src_series, adj_macd_fast, adj_macd_slow, adj_macd_sig) // 计算MACD值

    is_macd_golden_series = ta.crossover(macdLine, signalLine) // MACD金叉信号 (series bool)
    is_macd_death_series = ta.crossunder(macdLine, signalLine) // MACD死叉信号 (series bool)

    // RSI 计算
    // 根据时间周期乘数调整RSI参数
    adj_rsi_len = rsi_len_const * tf_multiplier_const
    rsi_val_series = ta.rsi(src_series, adj_rsi_len) // 计算RSI值 (series float)
    is_rsi_oversold_exit_series = ta.crossover(rsi_val_series, rsi_os_const)     // RSI 从超卖区上穿 (series bool)
    is_rsi_overbought_exit_series = ta.crossunder(rsi_val_series, rsi_ob_const) // RSI 从超买区下穿 (series bool)

    // 信号逻辑判断
    if indicator_name == OPT_NONE // 如果选择 "None"
        signal_bool := true // 信号始终为 true
    else if indicator_name == OPT_MACD_CROSS // 如果选择 "MACD Cross"
        signal_bool := is_macd_golden_series or is_macd_death_series // 金叉或死叉都算信号
    else if indicator_name == OPT_MACD_GOLDEN // 如果选择 "MACD Golden Cross"
        signal_bool := is_macd_golden_series // 仅金叉算信号
    else if indicator_name == OPT_MACD_DEATH // 如果选择 "MACD Death Cross"
        signal_bool := is_macd_death_series // 仅死叉算信号
    else if indicator_name == OPT_RSI_OBOS // 如果选择 "RSI Overbought/Oversold"
        // 多头: MACD金叉 或 RSI从超卖区上穿
        // 空头: MACD死叉 或 RSI从超买区下穿
        signal_bool := is_long_trade_series ? (is_macd_golden_series or is_rsi_oversold_exit_series) : (is_macd_death_series or is_rsi_overbought_exit_series)

    signal_bool // 返回最终的布尔信号 (series bool)

// ========== Trading Logic (交易逻辑) ==========
is_long = i_trade_direction == "Long" // 根据输入判断当前策略是做多还是做空 (series bool, 但在此上下文中通常在每根K线开始时评估一次)

// --- Initial Entry (初始入场) ---
// 使用其自身的时间周期乘数计算初始入场信号
initial_entry_signal = f_get_specific_indicator_signal(
     close,                             // 使用收盘价作为源数据
     i_initial_entry_indicator,         // 初始入场指标类型 (simple string)
     i_macd_fast, i_macd_slow, i_macd_signal, // MACD参数 (simple int)
     i_rsi_length, i_rsi_overbought, i_rsi_oversold, // RSI参数 (simple int)
     i_initial_entry_tf_multiplier,     // 初始入场时间周期乘数 (simple int)
     is_long                            // 当前交易方向 (series bool)
 )

if num_open_orders == 0 and initial_entry_signal // 如果当前没有持仓订单 并且 初始入场信号触发
    entry_size_contracts = i_initial_order_size_usd / close // 以美元计价的订单大小转换为合约数量
    current_trade_id := is_long ? "LMart" : "SMart" // 设置当前交易周期的ID ("LMart"代表多头马丁, "SMart"代表空头马丁)
    if is_long
        strategy.entry(current_trade_id, strategy.long, qty=entry_size_contracts, comment="Initial Long") // 执行多头入场
    else
        strategy.entry(current_trade_id, strategy.short, qty=entry_size_contracts, comment="Initial Short") // 执行空头入场

    // 更新状态变量
    last_entry_price := close             // 记录当前收盘价为最后入场价
    avg_entry_price := close              // 初始订单的平均价即为当前价
    total_qty_contracts := entry_size_contracts // 更新总持仓合约数
    num_open_orders := 1                  // 已开订单数设为1

    // 计算止盈和止损价格
    if is_long
        take_profit_price := avg_entry_price * (1 + i_take_profit_pct) // 多头止盈价
        if i_use_stop_loss
            stop_loss_price := avg_entry_price * (1 - i_stop_loss_pct) // 多头止损价
    else
        take_profit_price := avg_entry_price * (1 - i_take_profit_pct) // 空头止盈价
        if i_use_stop_loss
            stop_loss_price := avg_entry_price * (1 + i_stop_loss_pct) // 空头止损价

// --- Averaging Down Entries (加仓摊平成本入场) ---
// 预先计算早期和晚期加仓时间周期乘数的信号。这些现在是 series bool。
signal_for_avg_early_tf = f_get_specific_indicator_signal (
     close, 
     i_avg_early_indicator,          // 早期加仓指标类型
     i_macd_fast, 
     i_macd_slow, 
     i_macd_signal, 
     i_rsi_length, 
     i_rsi_overbought, 
     i_rsi_oversold, 
     i_avg_early_tf_multiplier,      // 早期加仓时间周期乘数
     is_long
 )

signal_for_avg_late_tf = f_get_specific_indicator_signal(
     close,
     i_avg_late_indicator,           // 晚期加仓指标类型
     i_macd_fast, i_macd_slow, i_macd_signal,
     i_rsi_length, i_rsi_overbought, i_rsi_oversold,
     i_avg_late_tf_multiplier,       // 晚期加仓时间周期乘数
     is_long
 )

if num_open_orders > 0 and num_open_orders < 10 // 如果有持仓且未达到最大加仓次数 (最多9次加仓)
    current_add_order_idx = num_open_orders - 1 // 当前加仓订单的索引 (0对应订单2, 1对应订单3, ...)

    if array.get(arr_order_active, current_add_order_idx) // 如果当前层级的加仓订单已启用
        // 1. Price Condition (价格条件 - series bool)
        price_target_for_add_on = 0.0 // 初始化加仓目标价
        if is_long // 如果是多头
            price_target_for_add_on := last_entry_price * (1 - array.get(arr_order_drop_pct, current_add_order_idx)) // 计算多头加仓的目标价 (下跌一定百分比)
        else // 如果是空头
            price_target_for_add_on := last_entry_price * (1 + array.get(arr_order_drop_pct, current_add_order_idx)) // 计算空头加仓的目标价 (上涨一定百分比)

        price_condition_met = (is_long and close <= price_target_for_add_on) or 
                              (not is_long and close >= price_target_for_add_on) // 判断价格条件是否满足 (series bool)

        // 2. Indicator Condition (指标条件 - series bool)
        // num_open_orders 是 series int, 所以这个选择使得 'indicator_condition_met' 成为 series bool
        indicator_condition_met = false
        if num_open_orders < 4 // 对于订单2,3,4 (当 num_open_orders 为 1,2,3 时)
            indicator_condition_met := signal_for_avg_early_tf // 使用早期加仓信号
        else // 对于订单5-10
            indicator_condition_met := signal_for_avg_late_tf // 使用晚期加仓信号
        
        // 如果早期/晚期选择的指标类型是 OPT_NONE, 它已经被设为 true。

        if price_condition_met and indicator_condition_met // 如果价格条件和指标条件都满足
            add_on_size_usd = i_initial_order_size_usd * array.get(arr_order_size_mult, current_add_order_idx) // 计算加仓订单的美元大小
            add_on_contracts = add_on_size_usd / close // 转换为合约数量

            entry_comment = (is_long ? "Add Long " : "Add Short ") + str.tostring(num_open_orders + 1) // 构建订单注释
            if is_long
                strategy.entry(current_trade_id, strategy.long, qty=add_on_contracts, comment=entry_comment) // 执行多头加仓
            else
                strategy.entry(current_trade_id, strategy.short, qty=add_on_contracts, comment=entry_comment) // 执行空头加仓

            // 近似更新状态变量 (实际成交价可能略有差异，会在下一根K线开始时用 strategy.position_avg_price 校准)
            new_total_qty = total_qty_contracts + add_on_contracts // 新的总合约数 (近似)
            avg_entry_price := (total_qty_contracts * avg_entry_price + add_on_contracts * close) / new_total_qty // 新的平均入场价 (近似)
            total_qty_contracts := new_total_qty // 更新总合约数 (近似)
            last_entry_price := close            // 更新最后入场价
            num_open_orders += 1                 // 已开订单数增加 (series int, 逐K线变化)

            // 重新计算止盈/止损价格 (如果需要，将在下一根K线上使用 strategy.position_avg_price)
            if is_long
                take_profit_price := avg_entry_price * (1 + i_take_profit_pct)
                if i_use_stop_loss
                    stop_loss_price := avg_entry_price * (1 - i_stop_loss_pct)
            else
                take_profit_price := avg_entry_price * (1 - i_take_profit_pct)
                if i_use_stop_loss
                    stop_loss_price := avg_entry_price * (1 + i_stop_loss_pct)

// --- Exit Logic (出场逻辑) ---
if num_open_orders > 0 // 如果有持仓订单
    // 如果有实际持仓 (例如订单被TV引擎处理后)，并且脚本内部状态与实际状态不一致，则更新脚本内部状态
    // 这在每根K线开始时运行，使用前一根K线执行结果的值。
    if strategy.position_size != 0 and not na(strategy.position_avg_price) and num_open_orders > 0
        if math.abs(strategy.position_size) != total_qty_contracts or strategy.position_avg_price != avg_entry_price
            // 如果TV的实际持仓状态与我们追踪的状态不同 (例如，入场成交后)，则更新我们的状态。
            avg_entry_price := strategy.position_avg_price           // 使用实际的平均持仓价格
            total_qty_contracts := math.abs(strategy.position_size)  // 使用实际的持仓数量
            // num_open_orders 理论上应该与 pyramiding 保持一致，但如果需要也可以重新同步。

            // 根据更新后的实际平均价格重新计算止盈止损
            if is_long
                take_profit_price := avg_entry_price * (1 + i_take_profit_pct)
                if i_use_stop_loss
                    stop_loss_price := avg_entry_price * (1 - i_stop_loss_pct)
            else
                take_profit_price := avg_entry_price * (1 - i_take_profit_pct)
                if i_use_stop_loss
                    stop_loss_price := avg_entry_price * (1 + i_stop_loss_pct)

    // Take Profit (止盈)
    tp_hit = (is_long and close >= take_profit_price and not na(take_profit_price)) or 
             (not is_long and close <= take_profit_price and not na(take_profit_price)) // 判断是否触发止盈 (series bool)
    if tp_hit
        strategy.close(current_trade_id, comment="TP Hit") // 平掉当前交易ID下的所有仓位
        // 重置状态变量
        last_entry_price := na
        avg_entry_price := na
        total_qty_contracts := 0
        num_open_orders := 0
        take_profit_price := na
        stop_loss_price := na
        current_trade_id := ""

    // Stop Loss (止损)
    if i_use_stop_loss and not na(stop_loss_price) // 如果启用了止损且止损价有效
        sl_hit = (is_long and close <= stop_loss_price) or 
                 (not is_long and close >= stop_loss_price) // 判断是否触发止损 (series bool)
        if sl_hit
            strategy.close(current_trade_id, comment="SL Hit") // 平掉当前交易ID下的所有仓位
            // 重置状态变量
            last_entry_price := na
            avg_entry_price := na
            total_qty_contracts := 0
            num_open_orders := 0
            take_profit_price := na
            stop_loss_price := na
            current_trade_id := ""

// 如果仓位被TV平掉 (例如回测结束，或从图表手动平仓)
// 而我们的状态仍然认为订单是开着的，则重置我们的状态。
if strategy.position_size == 0 and num_open_orders > 0
    last_entry_price := na
    avg_entry_price := na
    total_qty_contracts := 0
    num_open_orders := 0
    take_profit_price := na
    stop_loss_price := na
    current_trade_id := ""

// ========== Plotting (绘图) ==========
plot(num_open_orders > 0 ? avg_entry_price : na, "Avg Entry Price", color=color.blue, style=plot.style_linebr, linewidth=2) // 绘制平均入场价
plot(num_open_orders > 0 ? take_profit_price : na, "Take Profit", color=color.green, style=plot.style_linebr, linewidth=2)   // 绘制止盈价
plot(num_open_orders > 0 and i_use_stop_loss ? stop_loss_price : na, "Stop Loss", color=color.red, style=plot.style_linebr, linewidth=2) // 绘制止损价 (如果启用)

// --- 绘制下一个潜在加仓点位 ---
float next_potential_entry_price_for_plot = na  // 初始化下一个潜在入场价 (用于绘图)
string next_potential_entry_label_text = ""    // 初始化下一个潜在入场标签文本

if num_open_orders > 0 and num_open_orders < 10 // 如果有持仓且未达到最大加仓数
    current_add_order_idx_plot = num_open_orders - 1 // 当前加仓订单的索引
    if array.get(arr_order_active, current_add_order_idx_plot) // 如果该层加仓已启用
        next_potential_entry_price_for_plot := is_long ?
                                             last_entry_price * (1 - array.get(arr_order_drop_pct, current_add_order_idx_plot)) : 
                                             last_entry_price * (1 + array.get(arr_order_drop_pct, current_add_order_idx_plot)) // 计算下一个潜在加仓价格
        next_potential_entry_label_text := "NPE " + str.tostring(num_open_orders + 1) // NPE = Next Potential Entry, 加上订单序号
        
plot(series=next_potential_entry_price_for_plot,
     title="Next Potential Entry Level", 
     color=color.new(color.orange, 50), // 橙色，50%透明度
     style=plot.style_circles,          // 圆圈样式
     linewidth=1)                       // 线宽

if not na(next_potential_entry_price_for_plot) // 如果下一个潜在入场价有效
    label.new(x=bar_index, y=next_potential_entry_price_for_plot, // 在当前K线的对应价格位置创建标签
              text=next_potential_entry_label_text,            // 标签文本
              xloc=xloc.bar_index, yloc=yloc.price,            // 标签定位
              color=color.new(color.orange, 100),              // 标签背景色 (完全透明的橙色，所以不可见)
              textcolor=color.new(color.black, 0),             // 文本颜色 (黑色，不透明)
              style=label.style_none,                          // 标签样式 (无边框)
              textalign=text.align_center,                     // 文本居中对齐
              size=size.small)                                 // 文本大小

plotchar(num_open_orders, "Num Open Orders", "", location.top, size=size.tiny) // 在图表顶部用字符形式显示当前已开订单数量