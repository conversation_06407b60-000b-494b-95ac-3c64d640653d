//@version=5
strategy("KNN多因子量化策略", overlay=true, margin_long=100, margin_short=100)

// ========== 输入参数 ==========
// KNN预测参数
knn_length = input.int(14, "KNN长度", minval=1)
knn_k = input.int(5, "K值", minval=1)

// 移动平均线参数
ma_length = input.int(21, "移动平均线长度", minval=1)

// MACD参数
macd_fast = input.int(12, "MACD快线", minval=1)
macd_slow = input.int(26, "MACD慢线", minval=1)
macd_signal = input.int(9, "MACD信号线", minval=1)

// QQE参数
qqe_length = input.int(14, "QQE长度", minval=1)
qqe_smooth = input.int(5, "QQE平滑", minval=1)

// TDF参数
tdf_threshold = input.float(0.3, "TDF阈值", minval=0.1, maxval=1.0)

// 自适应参数
use_adaptive = input.bool(true, "使用自适应参数")
volatility_period = input.int(20, "波动率计算周期")
vol_factor = input.float(1.5, "波动率调整因子")

// 计算波动率
atr_val = ta.atr(volatility_period)
norm_atr = atr_val / close * 100  // 归一化ATR

// 自适应参数调整
adaptive_tdf_threshold = use_adaptive ? math.max(0.1, math.min(1.0, tdf_threshold * (1 + (norm_atr - ta.sma(norm_atr, 100)) / 100 * vol_factor))) : tdf_threshold

// 市场状态过滤器
market_filter_length = input.int(200, "市场过滤器长度")
market_filter_threshold = input.float(0.3, "市场过滤器阈值")

// 计算市场状态
market_trend = ta.ema(close, 50) - ta.ema(close, 200)
market_volatility = ta.stdev(ta.roc(close, 1), 20)
market_efficiency_ratio = math.abs(ta.change(close, market_filter_length)) /
                         ta.sum(math.abs(ta.change(close)), market_filter_length)

// 市场状态判断
is_trending_market = market_efficiency_ratio > market_filter_threshold
is_volatile_market = market_volatility > ta.sma(market_volatility, 100) * 1.5

// 背景颜色显示
show_bg = input.bool(true, "显示背景颜色")

// ========== 技术指标计算 ==========

// 改进的KNN预测函数
f_knn_predict(src, length, k) =>
    var distances = array.new_float(length, 0.0)
    var patterns = array.new_float(length, 0.0)
    var prediction = 0.0

    // 当前价格模式
    current_pattern = array.new_float(5, 0.0)
    for i = 0 to 4
        array.set(current_pattern, i, src[i] / src[0] - 1)

    // 计算历史模式与当前模式的距离
    for i = 0 to length - 1
        hist_pattern = array.new_float(5, 0.0)
        for j = 0 to 4
            array.set(hist_pattern, j, src[i+j+5] / src[i+5] - 1)

        // 欧氏距离计算
        dist = 0.0
        for j = 0 to 4
            dist += math.pow(array.get(current_pattern, j) - array.get(hist_pattern, j), 2)
        dist := math.sqrt(dist)

        array.set(distances, i, dist)
        array.set(patterns, i, src[i+5] > src[i+6] ? 1.0 : -1.0)

    // 找出k个最近邻
    sorted_indices = array.new_int(length, 0)
    for i = 0 to length - 1
        array.set(sorted_indices, i, i)

    // 简单排序找最近的k个点
    for i = 0 to length - 1
        for j = i + 1 to length - 1
            if array.get(distances, array.get(sorted_indices, i)) > array.get(distances, array.get(sorted_indices, j))
                temp = array.get(sorted_indices, i)
                array.set(sorted_indices, i, array.get(sorted_indices, j))
                array.set(sorted_indices, j, temp)

    // 计算预测值
    sum = 0.0
    for i = 0 to math.min(k-1, length-1)
        sum += array.get(patterns, array.get(sorted_indices, i))

    prediction := sum / math.min(k, length)
    prediction

// 移动平均线
knnMA_ = ta.sma(close, ma_length)

// MACD指标
[macd_val, macd_signal_line, macd_histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)

// QQE指标（简化版本）
rsi_val = ta.rsi(close, qqe_length)
qqe_val = ta.ema(rsi_val, qqe_smooth)
qqeDownSignal = ta.crossunder(qqe_val, 70)

// TDF指标（时间衰减因子）
tdf = math.abs(ta.change(close)) / ta.atr(14)

// 特征工程 - 价格动量和波动率特征
momentum_1 = ta.roc(close, 1)
momentum_5 = ta.roc(close, 5)
momentum_10 = ta.roc(close, 10)

// 波动率特征
volatility_ratio = ta.atr(5) / ta.atr(20)
price_range = (high - low) / close * 100

// 交易量特征
volume_change = ta.change(volume) / volume * 100
volume_ma_ratio = volume / ta.sma(volume, 20)

// 调用KNN预测函数
knn_prediction = f_knn_predict(close, knn_length, knn_k)

// 综合预测分数
long_score = 0.0
short_score = 0.0

// 多单评分
long_score := long_score + (knn_prediction > 0 ? 1 : -1) * 2
long_score := long_score + (momentum_1 > 0 ? 1 : -1)
long_score := long_score + (momentum_5 > 0 ? 1 : -1)
long_score := long_score + (momentum_10 > 0 ? 1 : -1)
long_score := long_score + (macd_val > 0 ? 1 : -1) * 1.5
long_score := long_score + (macd_val > macd_signal_line ? 1 : -1)
long_score := long_score + (rsi_val < 70 ? 1 : -1)
long_score := long_score + (volume_ma_ratio > 1 ? 1 : -1) * 0.5

// 空单评分
short_score := short_score + (knn_prediction < 0 ? 1 : -1) * 2
short_score := short_score + (momentum_1 < 0 ? 1 : -1)
short_score := short_score + (momentum_5 < 0 ? 1 : -1)
short_score := short_score + (momentum_10 < 0 ? 1 : -1)
short_score := short_score + (macd_val < 0 ? 1 : -1) * 1.5
short_score := short_score + (macd_val < macd_signal_line ? 1 : -1)
short_score := short_score + (rsi_val > 30 ? 1 : -1)
short_score := short_score + (volume_ma_ratio > 1 ? 1 : -1) * 0.5

// ========== 策略条件定义 ==========

// === 传统进场条件（备用） ===
long_bg = knn_prediction > -0.5                    // 绿背景
long_up3 = close > close[1] and close[1] > close[2] and close[2] > close[3]  // 三连涨
long_green3 = knnMA_ > knnMA_[1] and knnMA_[1] > knnMA_[2] and knnMA_[2] > knnMA_[3]  // MA三连升
not_ob = not ta.crossover(macd_val, 100)           // 非进入超买

long_entry_traditional = long_bg and long_up3 and long_green3 and not_ob

// === 空单进场条件（备用） ===
short_bg = knn_prediction < 0.5                    // 红背景
short_dn3 = close < close[1] and close[1] < close[2] and close[2] < close[3]  // 三连跌
short_red3 = knnMA_ < knnMA_[1] and knnMA_[1] < knnMA_[2] and knnMA_[2] < knnMA_[3]  // MA三连降
not_os = not ta.crossunder(macd_val, -100)         // 非进入超卖

short_entry_traditional = short_bg and short_dn3 and short_red3 and not_os

// === 出场条件 ===
// 先设初始变量（用 var 做持有记录和入场时tdf）
var float long_tdf_entry = na
var float short_tdf_entry = na
var bool in_long = false
var bool in_short = false

// 平仓判断
long_exit_1 = qqeDownSignal                        // QQE 红线
long_exit_2 = tdf >= adaptive_tdf_threshold and (in_long and long_tdf_entry < adaptive_tdf_threshold)

short_exit_1 = ta.crossover(qqe_val, 30)          // QQE反转
short_exit_2 = tdf >= adaptive_tdf_threshold and (in_short and short_tdf_entry < adaptive_tdf_threshold)

// 改进的进场条件 - 根据市场状态调整
long_entry_base = long_score >= 4 and not in_long and not in_short
short_entry_base = short_score >= 4 and not in_short and not in_long

// 根据市场状态调整策略
long_entry = if is_trending_market
    // 趋势市场下使用更激进的参数
    long_score >= 3 and market_trend > 0 and not in_long and not in_short
else if is_volatile_market
    // 高波动市场下更保守
    long_score >= 5 and not in_long and not in_short
else
    long_entry_base

short_entry = if is_trending_market
    // 趋势市场下使用更激进的参数
    short_score >= 3 and market_trend < 0 and not in_short and not in_long
else if is_volatile_market
    // 高波动市场下更保守
    short_score >= 5 and not in_short and not in_long
else
    short_entry_base

// 动态止损和止盈
atr_multiple = input.float(2.0, "ATR倍数", minval=0.5)
use_trailing_stop = input.bool(true, "使用追踪止损")
trailing_percent = input.float(1.0, "追踪止损百分比", minval=0.1)

// 多单进场
if long_entry and not in_long
    strategy.entry("Long", strategy.long)
    long_tdf_entry := tdf
    in_long := true
    in_short := false

// 空单进场
if short_entry and not in_short
    strategy.entry("Short", strategy.short)
    short_tdf_entry := tdf
    in_short := true
    in_long := false

// 多单出场
if (long_exit_1 or long_exit_2) and in_long
    strategy.close("Long")
    in_long := false
    long_tdf_entry := na

// 空单出场
if (short_exit_1 or short_exit_2) and in_short
    strategy.close("Short")
    in_short := false
    short_tdf_entry := na

// ========== 可视化显示 ==========

// 背景颜色
bgcolor(show_bg and long_bg ? color.new(color.green, 90) : na, title="多单背景")
bgcolor(show_bg and short_bg ? color.new(color.red, 90) : na, title="空单背景")

// 绘制移动平均线
plot(knnMA_, color=color.blue, linewidth=2, title="KNN移动平均线")

// 绘制进场信号
plotshape(long_entry, style=shape.triangleup, location=location.belowbar,
         color=color.green, size=size.small, title="多单进场")
plotshape(short_entry, style=shape.triangledown, location=location.abovebar,
         color=color.red, size=size.small, title="空单进场")

// 绘制出场信号
plotshape(long_exit_1 or long_exit_2, style=shape.xcross, location=location.abovebar,
         color=color.orange, size=size.small, title="多单出场")
plotshape(short_exit_1 or short_exit_2, style=shape.xcross, location=location.belowbar,
         color=color.purple, size=size.small, title="空单出场")

// ========== 信息面板 ==========
if barstate.islast
    var table info_table = table.new(position.top_right, 3, 10, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "指标", text_color=color.black, bgcolor=color.silver)
    table.cell(info_table, 1, 0, "数值", text_color=color.black, bgcolor=color.silver)
    table.cell(info_table, 2, 0, "状态", text_color=color.black, bgcolor=color.silver)

    table.cell(info_table, 0, 1, "KNN预测", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(knn_prediction, "#.####"), text_color=color.black)
    table.cell(info_table, 2, 1, knn_prediction > 0 ? "看涨" : "看跌",
              text_color=knn_prediction > 0 ? color.green : color.red)

    table.cell(info_table, 0, 2, "多单评分", text_color=color.black)
    table.cell(info_table, 1, 2, str.tostring(long_score, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 2, long_score >= 4 ? "强烈看涨" : long_score >= 2 ? "看涨" : "中性",
              text_color=long_score >= 4 ? color.green : long_score >= 2 ? color.lime : color.gray)

    table.cell(info_table, 0, 3, "空单评分", text_color=color.black)
    table.cell(info_table, 1, 3, str.tostring(short_score, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 3, short_score >= 4 ? "强烈看跌" : short_score >= 2 ? "看跌" : "中性",
              text_color=short_score >= 4 ? color.red : short_score >= 2 ? color.orange : color.gray)

    table.cell(info_table, 0, 4, "市场状态", text_color=color.black)
    table.cell(info_table, 1, 4, is_trending_market ? "趋势" : "震荡", text_color=color.black)
    table.cell(info_table, 2, 4, is_volatile_market ? "高波动" : "低波动",
              text_color=is_volatile_market ? color.orange : color.blue)

    table.cell(info_table, 0, 5, "持仓状态", text_color=color.black)
    table.cell(info_table, 1, 5, in_long ? "多单" : in_short ? "空单" : "空仓",
              text_color=in_long ? color.green : in_short ? color.red : color.gray)

// ========== 告警设置 ==========
alertcondition(long_entry, title="多单进场信号", message="KNN策略：多单进场信号触发")
alertcondition(short_entry, title="空单进场信号", message="KNN策略：空单进场信号触发")
alertcondition(long_exit_1 or long_exit_2, title="多单出场信号", message="KNN策略：多单出场信号触发")
alertcondition(short_exit_1 or short_exit_2, title="空单出场信号", message="KNN策略：空单出场信号触发")
