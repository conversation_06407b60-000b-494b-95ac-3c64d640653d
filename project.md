# KNN多因子量化策略项目文档

## 项目概述
这是一个基于K最近邻(KNN)算法的多因子量化交易策略，用于TradingView平台的Pine Script v5。

## 核心算法

### KNN预测算法
- **函数名**: `f_knn_predict(src, length, k)`
- **原理**: 通过分析历史价格模式，找出与当前价格模式最相似的K个历史时期，预测未来价格走向
- **特征**: 使用5个连续价格点构成价格模式，通过欧氏距离计算相似度
- **输出**: 返回-1到1之间的预测值，正值表示看涨，负值表示看跌

### 多因子评分系统
策略综合考虑以下因子：

#### 技术指标因子
1. **KNN预测** (权重: 2.0)
2. **动量指标**:
   - 1日动量 (权重: 1.0)
   - 5日动量 (权重: 1.0) 
   - 10日动量 (权重: 1.0)
3. **MACD指标** (权重: 1.5)
4. **RSI指标** (权重: 1.0)
5. **成交量比率** (权重: 0.5)

#### 市场状态过滤器
- **趋势市场**: 市场效率比率 > 阈值
- **高波动市场**: 当前波动率 > 历史平均波动率 * 1.5
- **自适应参数**: 根据ATR动态调整TDF阈值

## 策略逻辑

### 进场条件
根据市场状态采用不同的进场标准：

1. **趋势市场**: 评分 >= 3 且趋势方向一致
2. **高波动市场**: 评分 >= 5 (更保守)
3. **正常市场**: 评分 >= 4

### 出场条件
1. **QQE信号**: 
   - 多单: QQE下穿70
   - 空单: QQE上穿30
2. **TDF信号**: 时间衰减因子超过自适应阈值

### 风险管理
- **动态止损**: 基于ATR的自适应止损
- **追踪止损**: 可选的追踪止损功能
- **仓位管理**: 同时只允许一个方向的持仓

## 参数配置

### 核心参数
- **KNN长度**: 14 (历史数据窗口)
- **K值**: 5 (最近邻数量)
- **移动平均线长度**: 21
- **MACD参数**: 快线12, 慢线26, 信号线9
- **QQE参数**: 长度14, 平滑5
- **TDF阈值**: 0.3

### 自适应参数
- **波动率计算周期**: 20
- **波动率调整因子**: 1.5
- **市场过滤器长度**: 200
- **市场过滤器阈值**: 0.3

## 可视化功能

### 图表显示
1. **背景颜色**: 绿色(看涨)/红色(看跌)
2. **移动平均线**: 蓝色线条
3. **进场信号**: 三角形标记
4. **出场信号**: 十字标记

### 信息面板
实时显示：
- KNN预测值和方向
- 多空评分和状态
- 市场状态(趋势/震荡, 高/低波动)
- 当前持仓状态

### 告警功能
- 多单进场/出场信号
- 空单进场/出场信号

## 技术特点

### 优势
1. **多因子融合**: 综合多个技术指标，提高信号可靠性
2. **自适应机制**: 根据市场状态动态调整参数
3. **风险控制**: 完善的止损和仓位管理机制
4. **可视化**: 丰富的图表显示和信息面板

### 适用场景
- 中短期交易
- 趋势跟踪
- 震荡市场的反转交易
- 需要自动化信号的量化交易

## 文件结构
```
knn.pine - 主策略文件
project.md - 项目文档
```

## 版本信息
- Pine Script版本: v5
- 策略类型: 双向交易策略
- 更新日期: 2024年
